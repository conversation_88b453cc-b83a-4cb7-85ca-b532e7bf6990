--
-- PostgreSQL database dump
--

\restrict 8ln3blCnfbFGB8aoquWfZr631LyOekyNcRyZQ1UOu5VGo8rHbmZ0wky15vxcinT

-- Dumped from database version 15.14
-- Dumped by pg_dump version 15.14

\unrestrict 8ln3blCnfbFGB8aoquWfZr631LyOekyNcRyZQ1UOu5VGo8rHbmZ0wky15vxcinT
\set ON_ERROR_STOP off

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

INSERT INTO public.users (user_id, azure_ad_id, email, first_name, last_name, role, is_active, created_at, updated_at, created_by, updated_by, department, status, risk_score, last_login, two_factor_enabled) VALUES
('00000000-0000-0000-0000-000000000000', 'system', '<EMAIL>', 'System', 'User', 'system', true, '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00', NULL, NULL, NULL, 'active', 0.00, NULL, false),
('a1b2c3d4-e5f6-7890-abcd-111111111111', 'azure-admin-placeholder-001', '<EMAIL>', 'HIPAA', 'Administrator', 'admin', true, '2025-08-27 17:57:54.32967+00', '2025-08-27 17:57:54.32967+00', NULL, NULL, 'Information Technology', 'active', 0.00, NULL, true),
('a1b2c3d4-e5f6-7890-abcd-222222222222', 'azure-sarah-martinez-002', '<EMAIL>', 'Sarah', 'Martinez', 'user', true, '2025-08-27 17:57:54.32967+00', '2025-08-27 17:57:54.32967+00', NULL, NULL, 'Medical Affairs', 'active', 1.50, NULL, true),
('a1b2c3d4-e5f6-7890-abcd-333333333333', 'azure-jennifer-chen-003', '<EMAIL>', 'Jennifer', 'Chen', 'user', true, '2025-08-27 17:57:54.32967+00', '2025-08-27 17:57:54.32967+00', NULL, NULL, 'Privacy & Compliance', 'active', 0.50, NULL, true),
('a1b2c3d4-e5f6-7890-abcd-444444444444', 'azure-michael-rodriguez-004', '<EMAIL>', 'Michael', 'Rodriguez', 'user', true, '2025-08-27 17:57:54.32967+00', '2025-08-27 17:57:54.32967+00', NULL, NULL, 'Clinical Review', 'active', 1.00, NULL, false),
('a1b2c3d4-e5f6-7890-abcd-555555555555', 'azure-lisa-thompson-005', '<EMAIL>', 'Lisa', 'Thompson', 'user', true, '2025-08-27 17:57:54.32967+00', '2025-08-27 17:57:54.32967+00', NULL, NULL, 'Care Management', 'active', 0.80, NULL, true);


--
-- Data for Name: agents; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

COPY public.agents (agent_id, name, description, agent_type, endpoint_url, is_active, configuration, created_at, updated_at, created_by, vendor, department, risk_score, status, deleted_at) FROM stdin;
1b126746-792f-4a37-b052-537962196a48	Test Medical Agent	Test agent for medical privacy policy	policy_engine	\N	t	{}	2025-08-27 22:01:47.268285+00	2025-08-27 22:01:47.268285+00	00000000-0000-0000-0000-000000000000	\N	\N	0.00	active	\N
c1b2c3d4-e5f6-7890-abcd-111111111111	BCBS HIPAA Compliance Agent test	AI agent specialized in HIPAA compliance monitoring and PHI protection test	healthcare_hipaa_compliance	\N	t	{"auto_log_access": true, "audit_all_access": true, "redaction_enabled": true, "compliance_version": "HIPAA_2023", "emergency_override": true, "require_two_factor": false, "alert_on_violations": true, "max_session_duration": 3600, "supported_redaction_types": ["ssn", "phone", "address", "email", "dob", "insurance_id"]}	2025-08-27 17:57:54.32967+00	2025-08-28 05:08:06.220713+00	a1b2c3d4-e5f6-7890-abcd-111111111111	BCBS Blue Cross	Privacy & Compliance	0.20	active	\N
\.3333


--
-- Data for Name: roles; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

INSERT INTO public.roles (role_id, code, name, description, is_system_role, created_at, updated_at, created_by, permissions) VALUES
('b1b2c3d4-e5f6-7890-abcd-111111111111', 'HIPAA_COMPLIANCE_OFFICER', 'HIPAA Privacy and Compliance Officer', 'Responsible for HIPAA compliance monitoring and privacy oversight', false, '2025-08-27 17:57:54.32967+00', '2025-08-27 17:57:54.32967+00', NULL, '{}'),
('b1b2c3d4-e5f6-7890-abcd-222222222222', 'HIPAA_CLINICAL_REVIEWER', 'Clinical Staff with PHI Access', 'Healthcare staff authorized to review patient medical information', false, '2025-08-27 17:57:54.32967+00', '2025-08-27 17:57:54.32967+00', NULL, '{}'),
('b1b2c3d4-e5f6-7890-abcd-333333333333', 'HIPAA_MEDICAL_DIRECTOR', 'Senior Physician with Full Access', 'Senior medical staff with unrestricted access to patient data', false, '2025-08-27 17:57:54.32967+00', '2025-08-27 17:57:54.32967+00', NULL, '{}'),
('b1b2c3d4-e5f6-7890-abcd-444444444444', 'HIPAA_CASE_MANAGER', 'Patient Care Coordinator', 'Staff responsible for coordinating patient care across services', false, '2025-08-27 17:57:54.32967+00', '2025-08-27 17:57:54.32967+00', NULL, '{}'),
('b1b2c3d4-e5f6-7890-abcd-555555555555', 'HIPAA_ADMIN', 'System Administrator', 'Technical staff with system administration privileges', false, '2025-08-27 17:57:54.32967+00', '2025-08-27 17:57:54.32967+00', NULL, '{}');


--
-- Data for Name: agent_access; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

INSERT INTO public.agent_access (agent_id, role_id, access_level, granted_by) VALUES
('c1b2c3d4-e5f6-7890-abcd-111111111111', 'b1b2c3d4-e5f6-7890-abcd-111111111111', 'manage', 'a1b2c3d4-e5f6-7890-abcd-111111111111'),
('c1b2c3d4-e5f6-7890-abcd-111111111111', 'b1b2c3d4-e5f6-7890-abcd-222222222222', 'view', 'a1b2c3d4-e5f6-7890-abcd-111111111111'),
('c1b2c3d4-e5f6-7890-abcd-111111111111', 'b1b2c3d4-e5f6-7890-abcd-333333333333', 'manage', 'a1b2c3d4-e5f6-7890-abcd-111111111111'),
('c1b2c3d4-e5f6-7890-abcd-111111111111', 'b1b2c3d4-e5f6-7890-abcd-444444444444', 'view', 'a1b2c3d4-e5f6-7890-abcd-111111111111'),
('c1b2c3d4-e5f6-7890-abcd-111111111111', 'b1b2c3d4-e5f6-7890-abcd-555555555555', 'manage', 'a1b2c3d4-e5f6-7890-abcd-111111111111');


--
-- Data for Name: policies; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

INSERT INTO public.policies (policy_id, name, description, category, policy_type, definition, version, is_active, severity, applies_to_roles, created_at, updated_at, created_by, updated_by, rego_code, blob_container, blob_path, blob_url, rego_template_id, opa_sync_status, last_rego_generation, rego_generation_error, rego_version, original_policy_id, cloned_from_policy_name, deleted_at, guardrail_id) VALUES
('f1b2c3d4-e5f6-7890-abcd-111111111111', 'BCBS Minimum Necessary with Phone Redaction', 'Secure sharing of medical records with automatic PHI redaction', 'HIPAA Privacy Compliance', 'opa', '{"audit_redaction": true, "redaction_rules": {"phone": "({area_code}) ***-****"}, "redaction_fields": ["phone"], "emergency_override": true, "redaction_required": true, "external_sharing_allowed": false, "sharing_authorized_roles": ["HIPAA_CLINICAL_REVIEWER", "HIPAA_MEDICAL_DIRECTOR", "HIPAA_COMPLIANCE_OFFICER"]}', 1, 't', 'high', '{HIPAA_CLINICAL_REVIEWER,HIPAA_MEDICAL_DIRECTOR,HIPAA_COMPLIANCE_OFFICER}', '2025-08-27 17:57:54.32967+00', '2025-08-27 17:57:54.32967+00', 'a1b2c3d4-e5f6-7890-abcd-111111111111', NULL, NULL, 'rego-policies', NULL, NULL, NULL, 'pending', NULL, NULL, 1, NULL, NULL, NULL, NULL),
('f1b2c3d4-e5f6-7890-abcd-222222222222', 'BCBS Minimum Necessary with Email Redaction', 'Ensures only minimum necessary PHI is disclosed based on user role', 'HIPAA Privacy Compliance', 'opa', '{"visibility_rules": {"CASE_MANAGER": ["contact_info", "insurance_info"], "MEDICAL_DIRECTOR": ["all_fields"], "CLINICAL_REVIEWER": ["medical_info", "basic_demographics"], "COMPLIANCE_OFFICER": ["access_logs", "audit_trails"]}, "minimum_data_only": true, "redaction_methods": {"email": "****@****.com"}, "purpose_limitation": true, "pii_fields_to_redact": ["email"], "role_based_visibility": true, "auto_redaction_enabled": true}', 1, 't', 'high', '{HIPAA_CLINICAL_REVIEWER,HIPAA_MEDICAL_DIRECTOR,HIPAA_CASE_MANAGER}', '2025-08-27 17:57:54.32967+00', '2025-08-27 17:57:54.32967+00', 'a1b2c3d4-e5f6-7890-abcd-111111111111', NULL, NULL, 'rego-policies', NULL, NULL, NULL, 'pending', NULL, NULL, 1, NULL, NULL, NULL, NULL),
('f1b2c3d4-e5f6-7890-abcd-333333333333', 'BCBS Minimum Necessary with Auto-Redaction', 'Ensures only minimum necessary PHI is disclosed based on user role', 'HIPAA Privacy Compliance', 'opa', '{"visibility_rules": {"CASE_MANAGER": ["contact_info", "insurance_info"], "MEDICAL_DIRECTOR": ["all_fields"], "CLINICAL_REVIEWER": ["medical_info", "basic_demographics"], "COMPLIANCE_OFFICER": ["access_logs", "audit_trails"]}, "minimum_data_only": true, "redaction_methods": {"dob": "{month}/{day}/****", "ssn": "***-**-{last_4}", "email": "****@****.com", "phone": "({area_code}) ***-****", "address": "{city}, {state} {zip}", "insurance_id": "{first_3}*****"}, "purpose_limitation": true, "pii_fields_to_redact": ["ssn", "address", "phone", "email", "dob", "insurance_id"], "role_based_visibility": true, "auto_redaction_enabled": true}', 1, 't', 'high', '{HIPAA_CLINICAL_REVIEWER,HIPAA_MEDICAL_DIRECTOR,HIPAA_CASE_MANAGER}', '2025-08-27 17:57:54.32967+00', '2025-08-27 17:57:54.32967+00', 'a1b2c3d4-e5f6-7890-abcd-111111111111', NULL, NULL, 'rego-policies', NULL, NULL, NULL, 'pending', NULL, NULL, 1, NULL, NULL, NULL, NULL),
('00accc7f-7fd6-42cd-acbd-e9016b48eaec', 'Test Database Schema Policy', 'Testing policy creation with database schemas', 'Access Control', 'access_control', '{"type": "access_control", "enabled": true, "severity": "medium", "allowed_roles": ["admin", "user"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}', 1, 't', 'medium', NULL, '2025-08-27 22:17:01.52259+00', '2025-09-01 04:10:43.595437+00', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', NULL, 'rego-policies', NULL, NULL, NULL, 'pending', NULL, NULL, 1, NULL, NULL, NULL, NULL),
('4855b086-ab50-4488-9094-58f0be3f2cf3', 'Test Medical Privacy Policy', 'Testing complete policy lifecycle with database schemas', 'medical_privacy', 'medical_privacy', '{"type": "medical_privacy", "enabled": true, "severity": "high", "allowed_roles": ["doctor", "nurse", "admin"], "data_handling": {"anonymization": false, "pseudonymization": true, "data_minimization": true}, "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "lab_orders", "medical_record_number"], "audit_requirements": {"log_access": true, "access_timeout": 30, "retention_period": 7, "encryption_required": true}}', 1, 't', 'high', NULL, '2025-08-27 21:38:06.072074+00', '2025-09-01 04:11:33.146553+00', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', NULL, 'rego-policies', NULL, NULL, NULL, 'pending', NULL, NULL, 1, NULL, NULL, NULL, NULL),
('f4c1a348-7f94-4ed5-908c-4aee04e82f37', 'Test DB Schema Policy **********', 'Testing policy creation with database schemas', 'Access Control', 'access_control', '{"type": "access_control", "enabled": false, "severity": "medium", "allowed_roles": ["admin", "user"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00", "allowed_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "session_management": {"inactivity_timeout": 15, "concurrent_sessions": 3, "max_session_duration": 60}}', 1, 'f', 'medium', NULL, '2025-08-27 22:18:03.518299+00', '2025-09-01 04:11:55.191684+00', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', NULL, 'rego-policies', NULL, NULL, NULL, 'pending', NULL, NULL, 1, NULL, NULL, NULL, NULL),
('3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2', 'Demo Medical Privacy Policy', 'Test medical privacy policy for demo flow - UPDATED', 'medical_privacy', 'opa', '{"type": "medical_privacy", "enabled": false, "severity": "high", "allowed_roles": ["doctor", "nurse"], "data_handling": {"anonymization": false, "pseudonymization": true, "data_minimization": true}, "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "lab_orders"], "audit_requirements": {"log_access": true, "access_timeout": 30, "retention_period": 7, "encryption_required": true}}', 1, 'f', 'high', '{doctor,nurse}', '2025-08-28 04:27:16.865765+00', '2025-09-01 04:11:52.514032+00', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', NULL, 'rego-policies', NULL, NULL, NULL, 'pending', NULL, NULL, 1, NULL, NULL, NULL, NULL),
('5c36b6fd-c0ce-40e1-9681-75c2d500d588', 'Test Fix Policy **********', 'Testing after fixing JsonStructureGuide', 'data_privacy', 'opa', '{"type": "data_privacy", "enabled": true, "severity": "high", "allowed_roles": ["admin", "manager"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info", "financial_data"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}', 1, 't', 'high', '{admin,manager}', '2025-08-27 22:30:11.908103+00', '2025-08-28 04:04:53.021326+00', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', NULL, 'rego-policies', NULL, NULL, NULL, 'pending', NULL, NULL, 1, NULL, NULL, NULL, NULL),
('fb33044a-3bcc-4b23-9865-32131155c11c', 'E2E Emergency Policy 1', 'Emergency operational policy for testing', 'operational', 'e2e_comprehensive_test', '{"tags": ["emergency", "operational", "activated"], "enabled": true, "priority": 100, "policy_type": "emergency", "access_control": {"max_sessions": 1, "allowed_roles": ["admin"]}, "severity_level": "emergency"}', 1, 't', 'emergency', NULL, '2025-08-31 10:43:52.203793+00', '2025-08-31 10:44:13.70568+00', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', NULL, 'rego-policies', NULL, NULL, NULL, 'pending', NULL, NULL, 1, NULL, NULL, NULL, NULL);


--
-- Data for Name: agent_policies; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

INSERT INTO public.agent_policies (agent_id, policy_id, link_type) VALUES
('c1b2c3d4-e5f6-7890-abcd-111111111111', 'f1b2c3d4-e5f6-7890-abcd-111111111111', 'via_group'),
('c1b2c3d4-e5f6-7890-abcd-111111111111', 'f1b2c3d4-e5f6-7890-abcd-222222222222', 'via_group'),
('c1b2c3d4-e5f6-7890-abcd-111111111111', 'f1b2c3d4-e5f6-7890-abcd-333333333333', 'via_group');


--
-- Data for Name: policy_groups; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

INSERT INTO public.policy_groups (group_id, name, description, is_template, severity, status, version, tags, created_at, updated_at, created_by, deleted_at) VALUES
('11a01996-cb91-40ef-88c1-8e71328ca7f4', 'PI Bundle', 'PI Bun', true, 'medium', 'deprecated', 'v1.0.0', '{}', '2025-08-27 22:04:32.76169+00', '2025-09-01 04:46:00.200881+00', NULL, NULL),
('d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf', 'Test Medical Group', 'Test policy group for medical privacy policies', false, 'medium', 'active', 'v1.0.0', '{}', '2025-08-27 22:01:09.725675+00', '2025-08-27 22:01:09.725675+00', NULL, NULL),
('bf6bde98-2093-4da8-b6d8-3929ef674aff', 'Test Policy Group Demo', 'Test group for demo flow verification', false, 'medium', 'active', 'v1.0.0', '{}', '2025-08-28 04:25:26.763433+00', '2025-08-28 04:25:26.763433+00', NULL, NULL),
('d1b2c3d4-e5f6-7890-abcd-111111111111', 'HIPAA Compliance Policy Suite', 'Comprehensive HIPAA privacy and security policies for healthcare operations', false, 'medium', 'active', 'v1.0.0', '{"Patient Intake",CDI}', '2025-08-27 17:57:54.32967+00', '2025-08-29 22:03:18.942228+00', 'a1b2c3d4-e5f6-7890-abcd-111111111111', NULL),
('71e6509d-15af-44de-be9d-3b3040f7d6ed', 'E2E Test Policy Group', 'Policy group created for comprehensive end-to-end testing', false, 'high', 'active', 'v1.0.0', '{testing,e2e,automation,validation}', '2025-08-31 10:42:23.593801+00', '2025-08-31 10:42:23.593801+00', NULL, NULL);


--
-- Data for Name: policy_group_policies; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

INSERT INTO public.policy_group_policies (group_id, policy_id) VALUES
('d1b2c3d4-e5f6-7890-abcd-111111111111', 'f1b2c3d4-e5f6-7890-abcd-111111111111'),
('d1b2c3d4-e5f6-7890-abcd-111111111111', 'f1b2c3d4-e5f6-7890-abcd-222222222222'),
('d1b2c3d4-e5f6-7890-abcd-111111111111', 'f1b2c3d4-e5f6-7890-abcd-333333333333'),
('d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf', '4855b086-ab50-4488-9094-58f0be3f2cf3'),
('d7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf', '5c36b6fd-c0ce-40e1-9681-75c2d500d588'),
('bf6bde98-2093-4da8-b6d8-3929ef674aff', '3bee80ca-0c7f-4d78-9c4d-3ad2e4060ce2'),
('71e6509d-15af-44de-be9d-3b3040f7d6ed', 'fb33044a-3bcc-4b23-9865-32131155c11c');


--
-- Data for Name: agent_role_policies; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

INSERT INTO public.agent_role_policies (agent_id, role_id, group_id, policy_id, created_at) VALUES
('c1b2c3d4-e5f6-7890-abcd-111111111111', 'b1b2c3d4-e5f6-7890-abcd-111111111111', 'd7b4d8e1-6c6e-41fe-9e92-410ef99bc5bf', '5c36b6fd-c0ce-40e1-9681-75c2d500d588', '2025-08-28 05:14:25.257781+00'),
('c1b2c3d4-e5f6-7890-abcd-111111111111', 'b1b2c3d4-e5f6-7890-abcd-444444444444', 'd1b2c3d4-e5f6-7890-abcd-111111111111', 'f1b2c3d4-e5f6-7890-abcd-333333333333', '2025-09-01 04:16:14.781017+00');


--
-- Data for Name: alembic_version; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

-- No data to insert for alembic_version table


--
-- Data for Name: mcp_chat_sessions; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

-- No data to insert for mcp_chat_sessions table


--
-- Data for Name: chat_messages; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

-- No data to insert for chat_messages table


--
-- Data for Name: datasets; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

-- INSERT INTO public.datasets (id, name, description, type, status, data, record_count, created_by, created_at, updated_at) VALUES
-- No data to insert;


--
-- Data for Name: dataset_entries; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

-- INSERT INTO public.dataset_entries (id, dataset_id, test_case_type, input, expected_output, context, retrieval_context, tools_called, expected_outcome, scenario, initial_context, entry_order, created_at) VALUES
-- No data to insert;


--
-- Data for Name: documents; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

-- INSERT INTO public.documents (document_id, title, content, document_type, metadata, file_path, is_sensitive, created_at, updated_at, created_by, updated_by) VALUES
-- No data to insert;


--
-- Data for Name: enum_categories; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

INSERT INTO public.enum_categories (category_id, name, description, policy_type, field_path, is_active, created_at, updated_at) VALUES
(1, 'Medical Roles', 'Roles that can access medical data', 'medical_privacy', 'allowed_roles', 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(2, 'Medical Fields', 'Medical fields requiring special protection', 'medical_privacy', 'protected_fields', 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(3, 'Data Privacy Roles', 'Roles that can access sensitive data', 'data_privacy', 'allowed_roles', 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(4, 'Data Privacy Fields', 'Data fields requiring privacy protection', 'data_privacy', 'protected_fields', 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(5, 'Access Control Roles', 'Roles for access control policies', 'access_control', 'allowed_roles', 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(6, 'Compliance Roles', 'Roles for compliance policies', 'compliance', 'allowed_roles', 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(7, 'Severity Levels', 'Policy severity levels', 'all', 'severity', 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(8, 'e2e_policy_types', 'Policy types for E2E testing', 'e2e_comprehensive_test', 'policy_type', 't', '2025-08-31 10:42:10.855504+00', '2025-08-31 10:42:10.855504+00');


--
-- Data for Name: enum_values; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

INSERT INTO public.enum_values (value_id, category_id, value, display_name, description, sort_order, is_active, created_at, updated_at) VALUES
(1, 1, 'doctor', 'Doctor', 'Medical doctors with full access', 1, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(2, 1, 'nurse', 'Nurse', 'Nursing staff with patient care access', 2, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(3, 1, 'admin', 'Administrator', 'System administrators', 3, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(4, 1, 'pharmacist', 'Pharmacist', 'Pharmacy staff with medication access', 4, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(5, 1, 'lab_tech', 'Lab Technician', 'Laboratory technicians', 5, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(6, 1, 'specialist', 'Specialist', 'Medical specialists', 6, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(7, 1, 'resident', 'Resident', 'Medical residents', 7, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(8, 2, 'diagnosis', 'Diagnosis', 'Patient diagnosis information', 1, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(9, 2, 'medication', 'Medication', 'Prescribed medications', 2, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(10, 2, 'lab_orders', 'Lab Orders', 'Laboratory test orders', 3, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(11, 2, 'medical_record_number', 'Medical Record Number', 'Patient record identifier', 4, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(12, 2, 'treatment_plan', 'Treatment Plan', 'Patient treatment plans', 5, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(13, 2, 'billing_info', 'Billing Information', 'Medical billing data', 6, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(14, 2, 'patient_notes', 'Patient Notes', 'Clinical notes and observations', 7, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(15, 2, 'prescriptions', 'Prescriptions', 'Medication prescriptions', 8, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(16, 2, 'vital_signs', 'Vital Signs', 'Patient vital signs', 9, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(17, 2, 'allergies', 'Allergies', 'Patient allergy information', 10, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(18, 2, 'family_history', 'Family History', 'Patient family medical history', 11, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(19, 2, 'immunizations', 'Immunizations', 'Patient immunization records', 12, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(20, 3, 'admin', 'Administrator', 'System administrators', 1, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(21, 3, 'manager', 'Manager', 'Department managers', 2, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(22, 3, 'analyst', 'Analyst', 'Data analysts', 3, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(23, 3, 'user', 'User', 'General users', 4, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(24, 3, 'viewer', 'Viewer', 'Read-only users', 5, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(25, 3, 'editor', 'Editor', 'Content editors', 6, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(26, 4, 'personal_info', 'Personal Information', 'Personal identification data', 1, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(27, 4, 'contact_info', 'Contact Information', 'Contact details', 2, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(28, 4, 'financial_data', 'Financial Data', 'Financial information', 3, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(29, 4, 'employment_data', 'Employment Data', 'Employment information', 4, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(30, 4, 'health_data', 'Health Data', 'Health-related information', 5, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(31, 5, 'admin', 'Administrator', 'System administrators', 1, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(32, 5, 'manager', 'Manager', 'Department managers', 2, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(33, 5, 'user', 'User', 'General users', 3, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(34, 5, 'guest', 'Guest', 'Temporary users', 4, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(35, 6, 'compliance_officer', 'Compliance Officer', 'Compliance monitoring staff', 1, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(36, 6, 'auditor', 'Auditor', 'Internal auditors', 2, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(37, 6, 'legal', 'Legal', 'Legal department staff', 3, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(38, 6, 'admin', 'Administrator', 'System administrators', 4, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(39, 7, 'low', 'Low', 'Low priority issues', 1, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(40, 7, 'medium', 'Medium', 'Medium priority issues', 2, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(41, 7, 'high', 'High', 'High priority issues', 3, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00'),
(42, 7, 'critical', 'Critical', 'Critical priority issues', 4, 't', '2025-08-27 17:57:54.285266+00', '2025-08-27 17:57:54.285266+00');


--
-- Data for Name: evaluation_metrics; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

-- INSERT INTO public.evaluation_metrics (id, name, description, category, implementation_type, config, is_active, created_at, updated_at) VALUES
-- No data to insert;


--
-- Data for Name: experiments; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

-- INSERT INTO public.experiments (id, name, description, dataset_id, agent_config, execution_mode, status, progress, started_at, completed_at, created_by, created_at, updated_at) VALUES
-- No data to insert;


--
-- Data for Name: evaluations; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

-- INSERT INTO public.evaluations (id, evaluation_id, experiment_id, experiment_name, agent_name, dataset_name, evaluation_type, status, evaluations, summary, created_at, completed_at) VALUES
-- No data to insert;


--
-- Data for Name: experiment_evaluations; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

-- INSERT INTO public.experiment_evaluations (id, experiment_id, metric_id, status, score, details, created_at, updated_at) VALUES
-- No data to insert;


--
-- Data for Name: guardrail_services; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

-- INSERT INTO public.guardrail_services (id, service_id, name, version, type, endpoint, health_check_path, timeout_ms, capabilities, supported_content_types, status, last_health_check, metadata, created_at, updated_at) VALUES
-- No data to insert;


--
-- Data for Name: integration_dlq; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

-- INSERT INTO public.integration_dlq (id, tenant_id, destination, event_type, event_version, payload_json, attempts, last_error, dead_at, created_at, updated_at) VALUES
-- No data to insert;


--
-- Data for Name: integration_outbox; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

INSERT INTO public.integration_outbox (id, tenant_id, destination, event_type, event_version, payload_json, status, attempts, next_attempt_at, last_error, created_at, updated_at) VALUES
('18514264-c898-4ef6-9715-7beb4404ceee', NULL, 'webhook', 'policy.deleted', 1, '{"data": {"change_type": "deleted"}, "subject": {"resource_id": "3c7b0cb1-0c84-4c64-8ebb-bedc7c262e53", "resource_type": "policy"}}', 'pending', 0, '2025-09-01 04:42:28.431099+00', NULL, '2025-09-01 04:42:28.431099+00', '2025-09-01 04:42:28.431099+00'),
('fe02a614-8cc0-4fe1-9b0e-0476b9546216', NULL, 'webhook', 'policy.deleted', 1, '{"data": {"change_type": "deleted"}, "subject": {"resource_id": "62124064-1825-4bfa-99be-580b1a1c625d", "resource_type": "policy"}}', 'pending', 0, '2025-09-01 04:04:05.306125+00', NULL, '2025-09-01 04:04:05.306125+00', '2025-09-01 04:04:05.306125+00'),
('fe7be4f2-ba3f-4bd3-b811-364bb1a6b7ea', NULL, 'webhook', 'assignment.unlinked', 1, '{"data": {"change_type": "deleted"}, "subject": {"resource_id": {"role_id": "b1b2c3d4-e5f6-7890-abcd-444444444444", "agent_id": "00033e45-2454-42cc-b247-0c6e3d4e7e06", "group_id": "d1b2c3d4-e5f6-7890-abcd-111111111111", "policy_id": "9b3d812f-bde0-43e9-a211-5d48379f1320"}, "resource_type": "agent_role_policy"}}', 'pending', 0, '2025-09-01 04:04:05.306125+00', NULL, '2025-09-01 04:04:05.306125+00', '2025-09-01 04:04:05.306125+00'),
('71780871-1f7e-49d0-9d41-f83fb9f049b4', NULL, 'webhook', 'policy.updated', 1, '{"data": {"change_type": "updated"}, "subject": {"resource_id": "3c7b0cb1-0c84-4c64-8ebb-bedc7c262e53", "resource_type": "policy"}}', 'pending', 0, '2025-09-01 04:38:21.870957+00', NULL, '2025-09-01 04:38:21.870957+00', '2025-09-01 04:38:21.870957+00');


--
-- Data for Name: mcp_flow_steps; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

-- INSERT INTO public.mcp_flow_steps (step_id, session_id, step_number, step_name, status, input_data, output_data, processing_time_ms, policies_applied, violations_detected, created_at, completed_at) VALUES
-- No data to insert;


--
-- Data for Name: openai_api_calls; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

-- INSERT INTO public.openai_api_calls (call_id, session_id, step_id, model_name, prompt_tokens, completion_tokens, total_tokens, cost_estimate, response_time_ms, status_code, created_at) VALUES
-- No data to insert;


--
-- Data for Name: policy_executions; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

-- INSERT INTO public.policy_executions (execution_id, session_id, step_id, policy_id, execution_status, input_data, output_data, execution_time_ms, error_message, created_at) VALUES
-- No data to insert;


--
-- Data for Name: policy_schemas; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

INSERT INTO public.policy_schemas (id, schema_name, schema_content, description, guardrail_id, is_active, created_at, updated_at, default_template, template_source) VALUES
('9a9f7702-998f-407a-8253-10f61d94000f', 'medical_privacy', '{"type": "object", "title": "Medical Privacy Policy", "required": ["type", "severity", "allowed_roles", "protected_fields"], "properties": {"type": {"type": "string", "const": "medical_privacy", "description": "Policy type identifier"}, "enabled": {"type": "boolean", "default": true, "description": "Whether the policy is currently active"}, "severity": {"enum": ["low", "medium", "high", "critical"], "type": "string", "default": "medium", "description": "Policy severity level"}, "description": {"type": "string", "description": "Human-readable description of the policy"}, "allowed_roles": {"type": "array", "items": {"enum": ["doctor", "nurse", "admin", "pharmacist", "lab_tech", "specialist", "resident"], "type": "string"}, "minItems": 1, "description": "Roles with access to medical data"}, "data_handling": {"type": "object", "properties": {"anonymization": {"type": "boolean", "default": false, "description": "Anonymize data for research purposes"}, "pseudonymization": {"type": "boolean", "default": true, "description": "Use pseudonyms for patient identification"}, "data_minimization": {"type": "boolean", "default": true, "description": "Collect only necessary data"}}}, "hipaa_compliance": {"type": "boolean", "default": true, "description": "Enforce HIPAA compliance requirements"}, "protected_fields": {"type": "array", "items": {"enum": ["diagnosis", "medication", "lab_orders", "medical_record_number", "treatment_plan", "billing_info", "patient_notes", "prescriptions", "vital_signs", "allergies", "family_history", "immunizations"], "type": "string"}, "minItems": 1, "description": "Medical fields requiring special protection"}, "audit_requirements": {"type": "object", "required": ["log_access", "retention_period"], "properties": {"log_access": {"type": "boolean", "default": true, "description": "Log all access to medical data"}, "access_timeout": {"type": "integer", "default": 30, "maximum": 60, "minimum": 5, "description": "Session timeout in minutes"}, "retention_period": {"type": "integer", "default": 7, "maximum": 10, "minimum": 1, "description": "Audit log retention period in years"}, "encryption_required": {"type": "boolean", "default": true, "description": "Require encryption for medical data"}}}}, "description": "Template for HIPAA-compliant medical privacy policies", "additionalProperties": false}', 'Template for HIPAA-compliant medical privacy policies', NULL, 't', '2025-08-27 21:19:34.653695+00', '2025-08-29 12:55:30.994116+00', '{"type": "medical_privacy", "severity": "critical", "allowed_roles": ["doctor", "nurse", "admin"], "hipaa_compliance": true, "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan", "nn_test"]}', 'migrated_legacy'),
('f8870964-9455-4164-8a81-ba811fffdc98', 'access_control', '{"type": "object", "title": "Access Control Policy", "required": ["type", "severity", "allowed_roles"], "properties": {"type": {"type": "string", "const": "access_control", "description": "Policy type identifier"}, "enabled": {"type": "boolean", "default": true, "description": "Whether the policy is currently active"}, "severity": {"enum": ["low", "medium", "high", "critical"], "type": "string", "default": "medium", "description": "Policy severity level"}, "description": {"type": "string", "description": "Human-readable description of the policy"}, "ip_whitelist": {"type": "array", "items": {"type": "string", "pattern": "^(?:[0-9]{1,3}\\\\.){3}[0-9]{1,3}(?:/[0-9]{1,2})?$"}, "description": "Allowed IP addresses or CIDR ranges"}, "allowed_roles": {"type": "array", "items": {"enum": ["admin", "manager", "user", "viewer", "editor", "guest", "moderator"], "type": "string"}, "minItems": 1, "description": "Roles with access to the resource"}, "time_restrictions": {"type": "object", "required": ["start_time", "end_time"], "properties": {"end_time": {"type": "string", "default": "17:00", "pattern": "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", "description": "End time for access (24-hour format)"}, "timezone": {"type": "string", "default": "UTC", "description": "Timezone for time restrictions"}, "start_time": {"type": "string", "default": "09:00", "pattern": "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", "description": "Start time for access (24-hour format)"}, "allowed_days": {"type": "array", "items": {"enum": ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"], "type": "string"}, "default": ["monday", "tuesday", "wednesday", "thursday", "friday"], "description": "Days when access is allowed"}}}, "restricted_actions": {"type": "array", "items": {"enum": ["create", "read", "update", "delete", "export", "import", "share", "print"], "type": "string"}, "description": "Actions that are restricted for this resource"}, "session_management": {"type": "object", "required": ["max_session_duration", "inactivity_timeout"], "properties": {"inactivity_timeout": {"type": "integer", "default": 15, "maximum": 60, "minimum": 1, "description": "Inactivity timeout in minutes"}, "concurrent_sessions": {"type": "integer", "default": 3, "maximum": 10, "minimum": 1, "description": "Maximum concurrent sessions per user"}, "max_session_duration": {"type": "integer", "default": 60, "maximum": 480, "minimum": 5, "description": "Maximum session duration in minutes"}}}}, "description": "Template for role-based access control policies", "additionalProperties": false}', 'Template for role-based access control policies', NULL, 't', '2025-08-27 21:19:34.681926+00', '2025-08-29 12:01:08.784361+00', '{"type": "access_control", "severity": "high", "ip_whitelist": ["***********/24"], "allowed_roles": ["admin", "manager"], "time_restrictions": {"end_time": "17:00", "timezone": "UTC", "start_time": "09:00"}, "restricted_actions": ["delete", "export"]}', 'migrated_legacy'),
('f7f9e2a0-f912-4c8c-a078-6545ed80513c', 'data_privacy', '{"type": "object", "title": "Data Privacy Policy", "required": ["type", "severity", "allowed_roles", "data_classification", "protected_fields"], "properties": {"type": {"type": "string", "const": "data_privacy", "description": "Policy type identifier"}, "enabled": {"type": "boolean", "default": true, "description": "Whether the policy is currently active"}, "severity": {"enum": ["low", "medium", "high", "critical"], "type": "string", "default": "medium", "description": "Policy severity level"}, "description": {"type": "string", "description": "Human-readable description of the policy"}, "allowed_roles": {"type": "array", "items": {"enum": ["admin", "manager", "analyst", "user", "viewer", "editor"], "type": "string"}, "minItems": 1, "description": "Roles with access to sensitive data"}, "data_retention": {"type": "object", "required": ["retention_period"], "properties": {"archive_after": {"type": "integer", "default": 12, "maximum": 60, "minimum": 1, "description": "Archive data after this many months"}, "auto_deletion": {"type": "boolean", "default": true, "description": "Automatically delete expired data"}, "retention_period": {"type": "integer", "default": 24, "maximum": 120, "minimum": 1, "description": "Data retention period in months"}}}, "protected_fields": {"type": "array", "items": {"enum": ["personal_info", "financial_data", "contact_details", "identification", "preferences", "behavioral_data", "location_data", "biometric_data"], "type": "string"}, "minItems": 1, "description": "Data fields requiring protection"}, "data_classification": {"enum": ["public", "internal", "confidential", "restricted", "secret"], "type": "string", "default": "confidential", "description": "Classification level of the data"}, "consent_requirements": {"type": "object", "required": ["explicit_consent"], "properties": {"consent_expiry": {"type": "integer", "default": 12, "maximum": 60, "minimum": 1, "description": "Consent validity period in months"}, "explicit_consent": {"type": "boolean", "default": true, "description": "Require explicit user consent"}, "withdrawal_allowed": {"type": "boolean", "default": true, "description": "Allow users to withdraw consent"}}}}, "description": "Template for general data privacy and protection policies", "additionalProperties": false}', 'Template for general data privacy and protection policies', NULL, 't', '2025-08-27 21:19:34.667795+00', '2025-08-29 12:01:08.784361+00', '{"type": "data_privacy", "enabled": true, "severity": "medium", "allowed_roles": ["admin"], "data_retention": {"archive_after": 12, "auto_deletion": true, "retention_period": 24}, "protected_fields": ["personal_info"], "data_classification": "confidential", "consent_requirements": {"consent_expiry": 12, "explicit_consent": true, "withdrawal_allowed": true}}', 'auto_generated'),
('328e67f3-eac3-4eb6-9f75-ae1bda8455ac', 'compliance', '{"type": "object", "title": "Compliance Policy", "required": ["type", "severity", "allowed_roles", "regulatory_framework", "compliance_requirements"], "properties": {"type": {"type": "string", "const": "compliance", "description": "Policy type identifier"}, "enabled": {"type": "boolean", "default": true, "description": "Whether the policy is currently active"}, "severity": {"enum": ["low", "medium", "high", "critical"], "type": "string", "default": "medium", "description": "Policy severity level"}, "penalties": {"type": "object", "properties": {"monetary_fines": {"type": "boolean", "default": true, "description": "Apply monetary fines for violations"}, "max_fine_amount": {"type": "number", "minimum": 0, "description": "Maximum fine amount in currency units"}, "suspension_period": {"type": "integer", "maximum": 365, "minimum": 1, "description": "Account suspension period in days"}}}, "description": {"type": "string", "description": "Human-readable description of the policy"}, "allowed_roles": {"type": "array", "items": {"enum": ["compliance_officer", "auditor", "admin", "manager", "supervisor"], "type": "string"}, "minItems": 1, "description": "Roles responsible for compliance"}, "audit_frequency": {"enum": ["monthly", "quarterly", "semi_annually", "annually"], "type": "string", "default": "quarterly", "description": "Frequency of compliance audits"}, "regulatory_framework": {"enum": ["gdpr", "ccpa", "sox", "hipaa", "pci_dss", "iso27001", "ferpa"], "type": "string", "description": "Regulatory framework this policy enforces"}, "reporting_requirements": {"type": "object", "required": ["incident_reporting", "reporting_timeframe"], "properties": {"incident_reporting": {"type": "boolean", "default": true, "description": "Require incident reporting"}, "reporting_timeframe": {"type": "integer", "default": 24, "maximum": 72, "minimum": 1, "description": "Hours to report incidents"}, "regulatory_notifications": {"type": "boolean", "default": true, "description": "Notify regulatory bodies of incidents"}}}, "compliance_requirements": {"type": "array", "items": {"enum": ["data_encryption", "access_logging", "audit_trails", "consent_management", "data_minimization", "right_to_forget", "breach_notification", "vendor_management"], "type": "string"}, "minItems": 1, "description": "Specific compliance requirements to enforce"}}, "description": "Template for regulatory compliance policies", "additionalProperties": false}', 'Template for regulatory compliance policies', NULL, 't', '2025-08-27 21:19:34.692956+00', '2025-08-29 17:58:54.786951+00', '{"type": "compliance", "enabled": true, "severity": "medium", "penalties": {"monetary_fines": true}, "allowed_roles": ["compliance_officer"], "audit_frequency": "quarterly", "regulatory_framework": "gdpr", "reporting_requirements": {"incident_reporting": true, "reporting_timeframe": 24, "regulatory_notifications": true}, "compliance_requirements": ["data_encryption"]}', 'auto_generated');

--
-- Data for Name: policy_violations; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

-- INSERT INTO public.policy_violations (violation_id, policy_id, user_id, violation_type, details, severity, resolved, created_at, resolved_at, resolved_by) VALUES
-- No data to insert;


--
-- Data for Name: rego_templates; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

-- INSERT INTO public.rego_templates (template_id, name, description, policy_category, template_content, variables, is_active, created_at, updated_at, created_by) VALUES
-- No data to insert;


--
-- Data for Name: system_metrics; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

-- INSERT INTO public.system_metrics (metric_id, metric_type, metric_name, metric_value, dimensions, created_at) VALUES
-- No data to insert;


--
-- Data for Name: test_results; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

-- INSERT INTO public.test_results (id, experiment_id, dataset_entry_id, test_case_type, input, expected_output, actual_output, context, retrieval_context, tools_called, expected_outcome, scenario, status, score, latency_ms, token_count, metadata, created_at, updated_at) VALUES
-- No data to insert;


--
-- Data for Name: user_roles; Type: TABLE DATA; Schema: public; Owner: dbadmin
--

INSERT INTO public.user_roles (user_id, role_id) VALUES
('a1b2c3d4-e5f6-7890-abcd-111111111111', 'b1b2c3d4-e5f6-7890-abcd-555555555555'),
('a1b2c3d4-e5f6-7890-abcd-222222222222', 'b1b2c3d4-e5f6-7890-abcd-333333333333'),
('a1b2c3d4-e5f6-7890-abcd-222222222222', 'b1b2c3d4-e5f6-7890-abcd-222222222222'),
('a1b2c3d4-e5f6-7890-abcd-333333333333', 'b1b2c3d4-e5f6-7890-abcd-111111111111'),
('a1b2c3d4-e5f6-7890-abcd-444444444444', 'b1b2c3d4-e5f6-7890-abcd-222222222222'),
('a1b2c3d4-e5f6-7890-abcd-555555555555', 'b1b2c3d4-e5f6-7890-abcd-444444444444');


--
-- Name: enum_categories_category_id_seq; Type: SEQUENCE SET; Schema: public; Owner: dbadmin
--

SELECT pg_catalog.setval('public.enum_categories_category_id_seq', 8, true);


--
-- Name: enum_values_value_id_seq; Type: SEQUENCE SET; Schema: public; Owner: dbadmin
--

SELECT pg_catalog.setval('public.enum_values_value_id_seq', 42, true);


--
-- Name: evaluations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: dbadmin
--

SELECT pg_catalog.setval('public.evaluations_id_seq', 1, false);


--
-- PostgreSQL database dump complete
--

\unrestrict 8ln3blCnfbFGB8aoquWfZr631LyOekyNcRyZQ1UOu5VGo8rHbmZ0wky15vxcinT

